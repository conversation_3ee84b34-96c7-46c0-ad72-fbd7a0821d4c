Page({
  /**
   * 页面的初始数据
   */
  data: {
    supplyList: [], // 供应列表
    loading: false, // 加载状态
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    hasMore: true, // 是否有更多数据
    safeBottomHeight: 60, // 底部安全区域高度（单位rpx）
    searchKeyword: '', // 搜索关键词
    originalSupplyList: [], // 保存原始供应列表，用于搜索筛选
    total: 0, // 总记录数（用于显示供应数量）
    totalViews: 0, // 总浏览量
    isLogined: false, // 是否已登录
    userInfo: null, // 用户信息
    navHeight: 0, // 导航栏高度
    statusBarHeight: 0, // 状态栏高度
    weatherData: {}, // 天气数据
    weatherAnimation: true, // 天气图标动画状态
    weatherKey: 'ecea50b80c1543fdb0dac64b3d649332', // 和风天气API的key
    weatherLastUpdateTime: 0, // 天气数据最后更新时间戳
    registerTimeText: '', // 用户注册时间文本
    // 新增状态跟踪字段
    isDataLoaded: false, // 是否已加载过数据
    silentRefreshing: false, // 是否正在静默刷新
    lastRefreshTime: 0, // 上次刷新时间
  },

  // 添加变量跟踪天气信息请求时间
  lastWeatherInfoRequestTime: Date.now() - 10000, // 初始化为当前时间减去10秒，避免首次加载时触发频繁请求提示

  // 添加变量跟踪通过下拉刷新获取天气的时间
  lastPullRefreshWeatherTime: 0,

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化地图工具
    this.mapUtils = require('../../../utils/mapUtils.js');
    
    // 设置底部安全区域高度
    this.setSafeBottomHeight();
    
    // 获取设备信息，用于自适应布局
    this.setSystemInfo();
    
    // 确保底部tabBar正确显示"我"的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        active: 3,  // "我"页面是第四个选项，索引为3
        hidden: false // 确保tabbar不隐藏
      });
    }
    
    // 检查是否是报价类型页面
    if (options && (options.type === 'quote' || options.mode === 'quote')) {
      // 设置为报价模式
      this.setData({
        isQuoteMode: true,
        pageTitle: options.title || '我的报价'
      });
      
      // 修改导航栏标题
      wx.setNavigationBarTitle({
        title: options.title || '我的报价'
      });
    }
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 不管是否登录，都尝试加载数据
    this.setData({
      page: 1,
      supplyList: [],
      hasMore: true
    });
    
    // 根据模式加载不同数据
    if (this.data.isQuoteMode) {
      this.loadQuoteList();
    } else {
      this.loadSupplyList();
    }
    
    // 获取天气信息
    this.getWeatherInfo();
    
    // 启动天气图标动画
    this.startWeatherAnimation();
    
    // 获取用户注册时间
    if (this.data.isLogined && this.data.userInfo) {
      this.fetchUserRegisterTime();
    }
  },

  /**
   * 获取系统信息，用于自适应布局
   */
  setSystemInfo: function() {
    // 替换已弃用的wx.getSystemInfoSync()
    const deviceInfo = wx.getDeviceInfo();
    const windowInfo = wx.getWindowInfo();
    
    // 获取状态栏高度
    const statusBarHeight = windowInfo.statusBarHeight;
    // 默认导航栏高度
    const navHeight = (deviceInfo.platform === 'android' ? 48 : 44) + statusBarHeight;
    
    this.setData({
      statusBarHeight,
      navHeight
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 检查登录状态
    this.checkLoginStatus();

    // 智能刷新逻辑：只在必要时重新加载数据
    const currentTime = Date.now();
    const timeSinceLastRefresh = currentTime - this.data.lastRefreshTime;
    const REFRESH_INTERVAL = 30 * 1000; // 30秒内不重复刷新

    // 判断是否需要刷新数据
    const shouldRefresh = !this.data.isDataLoaded || // 首次加载
                         timeSinceLastRefresh > REFRESH_INTERVAL || // 超过刷新间隔
                         this.needsDataRefresh; // 标记需要刷新（从编辑页面返回）

    if (shouldRefresh) {
      // 如果是从编辑页面返回，强制重置分页状态并正常加载
      if (this.needsDataRefresh) {
        console.log('从编辑页面返回，重置分页状态并重新加载数据');
        this.setData({
          page: 1,
          supplyList: [],
          hasMore: true
        });

        // 使用正常加载而不是静默刷新，确保用户能看到加载状态
        if (this.data.isQuoteMode) {
          this.loadQuoteList();
        } else {
          this.loadSupplyList();
        }
      } else if (this.data.isDataLoaded && this.data.supplyList.length > 0) {
        // 常规静默刷新，保持用户浏览位置
        console.log('执行静默刷新，保持用户浏览位置');
        this.silentRefreshData();
      } else {
        // 首次加载或无数据时，正常加载
        this.setData({
          page: 1,
          supplyList: [],
          hasMore: true
        });

        // 根据模式加载不同数据
        if (this.data.isQuoteMode) {
          this.loadQuoteList();
        } else {
          this.loadSupplyList();
        }
      }
    }

    // 重置刷新标记
    this.needsDataRefresh = false;
    
    // 确保底部tabBar正确显示"我"的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        active: 3,  // "我"页面是第四个选项，索引为3
        hidden: false // 确保tabbar不隐藏
      });
    }
    
    // 仅在以下情况获取天气数据：
    // 1. 已登录状态
    // 2. 当前页面没有天气数据
    // 3. 距离上次请求已经超过一定时间
    const weatherCurrentTime = new Date().getTime();
    const timeSinceLastRequest = weatherCurrentTime - this.lastWeatherInfoRequestTime;
    const WEATHER_REFRESH_INTERVAL = 10 * 60 * 1000; // 10分钟刷新一次

    if (this.data.isLogined && 
        (!this.data.weatherData.temp || timeSinceLastRequest > WEATHER_REFRESH_INTERVAL)) {
      this.getWeatherInfo();
    }
    
    // 获取全局登录状态，确保头像实时更新
    const app = getApp();
    
    // 只有在全局已登录状态下才更新用户信息
    if (app.globalData.isLogined && app.globalData.userInfo && app.globalData.userId) {
      // 使用缓存的头像URL避免闪烁
      let userInfoWithAvatar = {...app.globalData.userInfo};
      if (app.globalData.cachedAvatarUrl) {
        userInfoWithAvatar.avatarUrl = app.globalData.cachedAvatarUrl;
      }

      // 立即使用全局数据更新UI，确保头像实时显示
      this.setData({
        isLogined: true,
        userInfo: userInfoWithAvatar
      });
      
      // 获取用户注册时间
      this.fetchUserRegisterTime();
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    // 获取App实例
    const app = getApp();
    
    // 从全局状态中获取登录状态
    if (app.globalData.isLogined && app.globalData.userInfo && app.globalData.userId) {
      // 使用缓存的头像URL避免闪烁
      let userInfoWithAvatar = {...app.globalData.userInfo};
      if (app.globalData.cachedAvatarUrl) {
        userInfoWithAvatar.avatarUrl = app.globalData.cachedAvatarUrl;
      }

      this.setData({
        isLogined: true,
        userInfo: userInfoWithAvatar
      });
      
      // 获取用户注册时间
      this.fetchUserRegisterTime();
      
      return true;
    } else {
      this.setData({
        isLogined: false,
        userInfo: null,
        registerTimeText: ''
      });
      return false;
    }
  },

  /**
   * 获取用户注册时间并计算至今时长
   */
  fetchUserRegisterTime: function() {
    const app = getApp();
    if (!app.globalData.userId) return;
    
    const db = wx.cloud.database();
    db.collection('users')
      .doc(app.globalData.userId)
      .field({
        createTime: true
      })
      .get()
      .then(res => {
        if (res.data && res.data.createTime) {
          // 计算注册时间
          const registerTime = this.calculateRegisterTime(res.data.createTime);
          this.setData({
            registerTimeText: registerTime
          });
        }
      })
      .catch(err => {
        console.error('获取用户注册时间失败:', err);
      });
  },

  /**
   * 静默刷新数据 - 在后台更新数据，不显示加载状态
   */
  silentRefreshData: function() {
    if (this.data.silentRefreshing) {
      return; // 防止重复刷新
    }

    this.setData({ silentRefreshing: true });

    // 保存当前数据作为备份，包括分页状态
    const backupData = {
      supplyList: [...this.data.supplyList],
      originalSupplyList: [...this.data.originalSupplyList],
      total: this.data.total,
      totalViews: this.data.totalViews,
      page: this.data.page,
      hasMore: this.data.hasMore
    };

    // 计算需要加载的页数（从第1页到当前页）
    const currentPage = this.data.page;
    const pageSize = this.data.pageSize;

    console.log(`静默刷新：当前在第${currentPage}页，需要重新加载前${currentPage}页的数据`);

    // 临时重置到第1页进行完整刷新
    const originalPage = this.data.page;
    this.setData({ page: 1 });

    // 静默加载第1页数据
    const refreshPromise = this.data.isQuoteMode ?
      this.loadQuoteList(true) : // 传入true表示静默刷新
      this.loadSupplyList(true);

    refreshPromise.then(() => {
      // 如果原来不在第1页，需要继续加载后续页面的数据
      if (originalPage > 1) {
        return this.loadAdditionalPagesForSilentRefresh(originalPage);
      }
    }).then(() => {
      this.setData({
        silentRefreshing: false,
        lastRefreshTime: Date.now(),
        isDataLoaded: true,
        page: originalPage // 恢复原来的页码
      });
      console.log('静默刷新完成，已恢复到原页码:', originalPage);
    }).catch((error) => {
      // 刷新失败时恢复备份数据
      this.setData({
        ...backupData,
        silentRefreshing: false
      });
      console.warn('静默刷新失败，已恢复原数据:', error);
    });
  },

  /**
   * 静默刷新时加载额外页面数据
   */
  loadAdditionalPagesForSilentRefresh: function(targetPage) {
    return new Promise((resolve, reject) => {
      const loadNextPage = (currentPage) => {
        if (currentPage > targetPage) {
          resolve();
          return;
        }

        this.setData({ page: currentPage });

        const loadPromise = this.data.isQuoteMode ?
          this.loadQuoteList(true) :
          this.loadSupplyList(true);

        loadPromise.then(() => {
          loadNextPage(currentPage + 1);
        }).catch(reject);
      };

      // 从第2页开始加载
      loadNextPage(2);
    });
  },

  /**
   * 标记需要数据刷新（从编辑页面返回时调用）
   */
  markNeedsRefresh: function() {
    this.needsDataRefresh = true;
    console.log('标记需要刷新数据，从编辑页面返回');
  },

  /**
   * 智能更新列表中的单个项目
   */
  updateSupplyItem: function(itemId, updatedData) {
    const supplyList = [...this.data.supplyList];
    const originalSupplyList = [...this.data.originalSupplyList];

    // 查找并更新项目
    const supplyIndex = supplyList.findIndex(item => item._id === itemId);
    const originalIndex = originalSupplyList.findIndex(item => item._id === itemId);

    if (supplyIndex !== -1) {
      supplyList[supplyIndex] = { ...supplyList[supplyIndex], ...updatedData };
    }

    if (originalIndex !== -1) {
      originalSupplyList[originalIndex] = { ...originalSupplyList[originalIndex], ...updatedData };
    }

    this.setData({
      supplyList,
      originalSupplyList
    });
  },

  /**
   * 计算注册时间
   * @param {Date|Object} createTime - 创建时间，可能是Date对象或云数据库时间对象
   * @returns {string} - 格式化的注册时间文本
   */
  calculateRegisterTime: function(createTime) {
    let createDate;
    
    // 处理云数据库时间对象
    if (createTime && createTime.$date) {
      createDate = new Date(createTime.$date);
    } 
    // 处理普通Date对象或时间戳
    else if (createTime instanceof Date) {
      createDate = createTime;
    } else if (typeof createTime === 'number') {
      createDate = new Date(createTime);
    } else {
      // 如果都不是，尝试直接创建Date对象
      try {
        createDate = new Date(createTime);
      } catch(e) {
        console.error('无法解析创建时间:', e);
        return '新用户';
      }
    }
    
    // 确保解析成功
    if (isNaN(createDate.getTime())) {
      return '新用户';
    }
    
    const now = new Date();
    const diffTime = Math.abs(now - createDate);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 1) {
      return '今日注册';
    } else if (diffDays < 30) {
      return `${diffDays}天前注册`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months}个月前注册`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years}年前注册`;
    }
  },

  /**
   * 获取用户最新信息，包括供应数量
   * 注意：我们不再需要这个方法，因为供应数量直接从loadSupplyList返回的total获取
   * 但保留方法以防其他地方调用，只是移除了对supplyCount的处理
   */
  fetchUserLatestInfo: function() {
    const app = getApp();
    if (!app.globalData.userId) return;
    
    const db = wx.cloud.database();
    db.collection('users')
      .doc(app.globalData.userId)
      .field({
        avatarUrl: true,
        nickName: true
      })
      .get()
      .then(res => {
        if (res.data) {
          // 使用缓存的头像URL，但更新其他信息
          let updatedUserInfo = { ...this.data.userInfo, ...res.data };
          
          if (app.globalData.cachedAvatarUrl) {
            updatedUserInfo.avatarUrl = app.globalData.cachedAvatarUrl;
          }
          
          this.setData({
            userInfo: updatedUserInfo
          });
        }
      })
      .catch(err => {
        console.error('获取用户最新信息失败:', err);
      });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({ 
      page: 1,
      supplyList: [],
      hasMore: true
    });
    
    // 根据模式加载不同数据
    const loadPromise = this.data.isQuoteMode ? 
      this.loadQuoteList() : 
      this.loadSupplyList();
      
    loadPromise.then(() => {
      wx.stopPullDownRefresh();
    });
    
    // 检查是否可以通过下拉刷新更新天气数据
    if (this.data.isLogined) {
      const currentTime = new Date().getTime();
      // 设置30分钟(1800000ms)的刷新限制
      const PULL_REFRESH_LIMIT = 30 * 60 * 1000; 
      
      // 检查是否超过了限制时间
      if (currentTime - this.lastPullRefreshWeatherTime >= PULL_REFRESH_LIMIT) {
        // 更新天气数据
        this.getWeatherInfo(true);
        // 更新最后一次通过下拉刷新获取天气的时间
        this.lastPullRefreshWeatherTime = currentTime;
      
      }
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },
  
  /**
   * 加载更多数据
   */
  loadMore: function() {
    this.setData({ page: this.data.page + 1 }, () => {
      // 根据模式加载不同数据
      if (this.data.isQuoteMode) {
        this.loadQuoteList();
      } else {
        this.loadSupplyList();
      }
    });
  },
  
  /**
   * 加载报价列表
   * @param {boolean} silent - 是否静默刷新，不显示loading状态
   */
  loadQuoteList: function(silent = false) {
    // 如果未登录，显示加载失败提示，但不跳转页面
    if (!this.data.isLogined) {
      this.setData({
        loading: false,
        supplyList: [],
        hasMore: false,
        total: 0
      });

      // 显示加载失败提示
      wx.showToast({
        title: '请先登录后查看',
        icon: 'none',
        duration: 2000
      });

      return Promise.resolve();
    }

    const { page, pageSize } = this.data;

    // 只在非静默刷新时显示loading状态
    if (!silent) {
      this.setData({ loading: true });
    }

    console.log(`加载报价列表：第${page}页，静默模式：${silent}`);
    
    // 检查当前用户是否为管理员
    const app = getApp();
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    const isAdmin = userInfo && userInfo.adm === true;
    
    // 根据用户角色选择不同的云函数
    const cloudFunctionType = isAdmin && this.data.isQuoteMode ? 'getAllQuoteList' : 'getMyQuoteList';
    
    // 更新标题，如果是管理员
    if (isAdmin && this.data.isQuoteMode) {
      wx.setNavigationBarTitle({
        title: '工程报价管理'
      });
      
      this.setData({
        pageTitle: '工程报价管理'
      });
    }
    
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: cloudFunctionType, // 根据用户角色使用不同的云函数类型
          page,
          pageSize
        },
        success: res => {
          const result = res.result || {};
          if (result.success) {
            const newData = result.data || [];
            
            // 处理数据，添加必要的格式化
            newData.forEach(item => {
              // 确保图片列表存在
              if (!item.imageList) {
                item.imageList = [];
              }
              
              // 确保状态文本和样式存在
              if (!item.statusText) {
                let statusText = '待处理';
                let statusClass = 'status-pending';
                
                if (item.status === 'completed') {
                  statusText = '已完成';
                  statusClass = 'status-completed';
                } 
                
                item.statusText = statusText;
                item.statusClass = statusClass;
              }
            });
            
            this.setData({
              supplyList: page === 1 ? newData : [...this.data.supplyList, ...newData],
              originalSupplyList: page === 1 ? newData : [...this.data.originalSupplyList, ...newData],
              hasMore: result.totalPages > page,
              total: result.total,
              loading: false,
              isAdmin: isAdmin, // 更新管理员状态
              isDataLoaded: true, // 标记数据已加载
              lastRefreshTime: Date.now() // 更新刷新时间
            });

            console.log(`报价列表加载完成：第${page}页，新增${newData.length}条数据，总计${this.data.supplyList.length}条`);
            
            // 如果是第一页且没有数据，显示空状态提示
            if (page === 1 && newData.length === 0) {
              wx.showToast({
                title: '暂无报价信息',
                icon: 'none'
              });
            }
          } else {
            // 显示错误提示
            wx.showToast({
              title: result.errMsg || '加载失败',
              icon: 'none'
            });
            this.setData({ loading: false });
          }
          resolve();
        },
        fail: err => {
          console.error('加载报价列表失败:', err);
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
          this.setData({ loading: false });
          reject(err);
        }
      });
    });
  },

  /**
   * 从云函数加载供应列表数据
   * @param {boolean} silent - 是否静默刷新，不显示loading状态
   */
  loadSupplyList: function(silent = false) {
    // 如果未登录，显示加载失败提示，但不跳转页面
    if (!this.data.isLogined) {
      this.setData({
        loading: false,
        supplyList: [],
        hasMore: false,
        total: 0, // 重置total为0
        totalViews: 0 // 重置totalViews为0
      });

      // 显示加载失败提示
      wx.showToast({
        title: '请先登录后查看',
        icon: 'none',
        duration: 2000
      });

      return Promise.resolve();
    }

    const { page, pageSize, searchKeyword } = this.data;

    // 只在非静默刷新时显示loading状态
    if (!silent) {
      this.setData({ loading: true });
    }

    console.log(`加载供应列表：第${page}页，静默模式：${silent}`);
    
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'getMySupplyList',
          page,
          pageSize,
          searchKeyword
        },
        success: res => {
          const result = res.result || {};
          if (result.code === 0) {
            const newData = result.data || [];
            
            // 处理数据，格式化日期和价格
            newData.forEach(item => {
              // 格式化价格
              if (item.price) {
                item.formattedPrice = `¥${item.price}`;
              } else {
                item.formattedPrice = '价格面议';
              }
              
              // 格式化创建时间
              if (item.createTime) {
                item.formattedTime = this.formatDate(new Date(item.createTime));
              }
              
              // 格式化拍照日期
              if (item.photoUpdated) {
                item.photoUpdated = this.formatDate(new Date(item.photoUpdated));
              } else {
                item.photoUpdated = '无拍照日期';
              }
            });
            
            // 计算总浏览量
            let totalViews = 0;
            if (page === 1) {
              // 如果是第一页，重新计算总浏览量
              newData.forEach(item => {
                totalViews += (item.viewCount || 0);
              });
            } else {
              // 如果不是第一页，累加浏览量
              totalViews = this.data.totalViews;
              newData.forEach(item => {
                totalViews += (item.viewCount || 0);
              });
            }
            
            this.setData({
              supplyList: page === 1 ? newData : [...this.data.supplyList, ...newData],
              originalSupplyList: page === 1 ? newData : [...this.data.originalSupplyList, ...newData],
              hasMore: result.hasMore,
              total: result.total, // 更新total用于显示供应数量
              totalViews: totalViews, // 更新总浏览量
              loading: false,
              isDataLoaded: true, // 标记数据已加载
              lastRefreshTime: Date.now() // 更新刷新时间
            });

            console.log(`供应列表加载完成：第${page}页，新增${newData.length}条数据，总计${this.data.supplyList.length}条`);
            
            // 如果是第一页且没有数据，显示空状态提示
            if (page === 1 && newData.length === 0) {
              wx.showToast({
                title: '暂无供应信息',
                icon: 'none'
              });
            }
          } else {
            // 显示错误提示
            wx.showToast({
              title: result.msg || '加载失败',
              icon: 'none'
            });
            this.setData({ loading: false });
          }
          resolve();
        },
        fail: err => {
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
          this.setData({ loading: false });
          reject(err);
        }
      });
    });
  },

  /**
   * 格式化日期
   * @param {Date} date - 日期对象
   * @returns {string} - 格式化的日期字符串 (YYYY-MM-DD)
   */
  formatDate: function(date) {
    if (!date || isNaN(date.getTime())) {
      return '无日期';
    }
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
  },

  /**
   * 查看详情
   */
  viewDetail: function(e) {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后查看',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    const id = e.currentTarget.dataset.id;
   
    // 根据页面模式决定跳转到不同的详情页面
    if (this.data.isQuoteMode) {
      // 报价模式，跳转到报价详情页
      wx.navigateTo({
        url: `/pages/quote/detail/detail?id=${id}`
      });
    } else {
      // 供应模式，跳转到供应详情页
      wx.navigateTo({
        url: `/pages/supply/detail/detail?id=${id}`
      });
    }
  },
  
  /**
   * 预览报价图片
   */
  previewQuoteImage: function(e) {
    const urls = e.currentTarget.dataset.urls;
    const current = e.currentTarget.dataset.current;
    
    if (!urls || urls.length === 0) return;
    
    wx.previewImage({
      current: urls[current], // 当前显示图片的链接
      urls: urls // 需要预览的图片链接列表
    });
  },

  /**
   * 更新供应
   */
  updateSupply: function(e) {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后更新',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 标记需要刷新数据
    this.markNeedsRefresh();

    const id = e.currentTarget.dataset.id;
    // 导航到编辑页面
    wx.navigateTo({
      url: `/pages/user/my-supply/edit/edit?id=${id}`
    });
  },

  /**
   * 下架供应
   */ 
  removeSupply: function(e) {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后操作',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认下架',
      content: '确定要下架这条供应信息吗？',
      success: res => {
        if (res.confirm) {
          // 模拟下架操作
          wx.showToast({
            title: '下架成功',
            icon: 'success'
          });
          
          // 刷新列表
          this.setData({ page: 1, supplyList: [] });
          this.loadSupplyList();
        }
      }
    });
  },

  /**
   * 导航栏返回按钮事件
   */
  onNavBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },
  
  /**
   * 导航栏首页按钮事件
   */
  onNavHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },
  
  /**
   * 跳转到发布供应页面
   */
  goToPublish: function() {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后发布',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/supply/publish/publish'
    });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin: function() {
    wx.switchTab({
      url: '/pages/user/user'
    });
  },

  /**
   * 设置底部安全区域高度
   */
  setSafeBottomHeight: function() {
    try {
      // 使用新API替换已弃用的wx.getSystemInfoSync()
      const windowInfo = wx.getWindowInfo();
      // 检测是否有底部安全区域（如iPhone X系列）
      let safeBottomHeight = 60; // 默认高度
      
      if (windowInfo.safeArea) {
        const safeAreaBottom = windowInfo.safeArea.bottom;
        const screenHeight = windowInfo.screenHeight;
        // 如果安全区域底部与屏幕底部有差距，说明有底部安全区域
        if (screenHeight > safeAreaBottom) {
          safeBottomHeight = (screenHeight - safeAreaBottom) * 2; // 转为rpx
        }
      }
      
      this.setData({ safeBottomHeight });
    } catch (e) {
      console.error('获取系统信息失败', e);
      // 使用默认值
      this.setData({ safeBottomHeight: 60 });
    }
  },

  /**
   * 搜索输入事件处理
   */
  onSearchInput: function(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  /**
   * 清空搜索
   */
  clearSearch: function() {
    this.setData({ 
      searchKeyword: '',
      page: 1,
      supplyList: [],
      hasMore: true
    });
    this.loadSupplyList();
  },

  /**
   * 执行搜索
   */
  onSearch: function() {
    this.setData({
      page: 1,
      supplyList: [],
      hasMore: true
    });
    this.loadSupplyList();
  },

  /**
   * 获取天气信息
   * @param {boolean} forceUpdate - 是否强制更新天气数据
   */
  getWeatherInfo: function(forceUpdate = false) {
    // 如果未登录，不获取天气信息
    if (!this.data.isLogined) return;
    
    // 获取当前时间
    const currentTime = new Date().getTime();
    
    // 检查是否在短时间内（3秒）重复调用，除非强制更新
    const MIN_REQUEST_INTERVAL = 3000; // 3秒内不重复请求
    if (!forceUpdate && (currentTime - this.lastWeatherInfoRequestTime < MIN_REQUEST_INTERVAL)) {
      console.log('天气信息请求过于频繁，跳过此次请求');
      return;
    }
    
    // 如果是强制更新，但是通过下拉刷新触发，需要检查是否超过了30分钟限制
    // 注意：这里我们通过比较lastPullRefreshWeatherTime是否为0来判断是否首次加载
    // 首次加载时，lastPullRefreshWeatherTime为0，应该允许获取天气数据
    if (forceUpdate && this.lastPullRefreshWeatherTime !== 0) {
      const PULL_REFRESH_LIMIT = 30 * 60 * 1000; // 30分钟
      if (currentTime - this.lastPullRefreshWeatherTime < PULL_REFRESH_LIMIT) {
        console.log('下拉刷新获取天气数据过于频繁，跳过此次请求');
        return;
      }
    }
    
    // 更新最后请求时间
    this.lastWeatherInfoRequestTime = currentTime;
    
    // 获取当前App实例，用于跨页面共享天气数据
    const app = getApp();
    
    // 检查全局天气数据是否已存在且未过期
    if (!forceUpdate && this.checkWeatherCache()) {
      console.log('使用缓存的天气数据');
      return;
    }
    
    // 使用mapUtils获取当前位置和位置名称
    this.mapUtils.getCurrentLocationAndReverse({
      success: (res) => {
        // 从结果中获取位置信息，按照腾讯地图API的返回结构处理
        const addressComponent = res.result.address_component;
        const location = res.result.location;
        
        const latitude = location.lat;
        const longitude = location.lng;
        const district = addressComponent.district;
        const city = addressComponent.city;
        
        // 更新位置名称
        this.setData({
          'weatherData.location': district || city || '未知位置'
        });
        
        // 获取天气数据
        this.getWeatherData(latitude, longitude);
      },
      fail: (err) => {
        console.error('获取位置失败', err);
        // 设置默认天气数据
        this.setDefaultWeatherData();
      }
    });
  },
  
  /**
   * 检查天气数据缓存是否有效
   * @returns {boolean} - 如果缓存有效返回true，否则返回false
   */
  checkWeatherCache: function() {
    const app = getApp();
    
    // 检查全局是否已有天气数据
    if (app.globalData && app.globalData.weatherData && 
        app.globalData.weatherData.temp && app.globalData.weatherData.text) {
      
      const currentTime = new Date().getTime();
      const lastUpdateTime = app.globalData.weatherLastUpdateTime || 0;
      const timeElapsed = currentTime - lastUpdateTime;
      
      // 如果天气数据是30分钟内获取的，则视为有效
      const CACHE_VALID_PERIOD = 30 * 60 * 1000; // 30分钟，单位毫秒
      
      if (timeElapsed < CACHE_VALID_PERIOD) {
        // 使用全局缓存的天气数据
        this.setData({
          weatherData: {...app.globalData.weatherData}
        });
        return true;
      }
    }
    
    return false;
  },

  // 标记是否正在请求天气数据，防止重复请求
  isRequestingWeather: false,

  /**
   * 获取天气数据
   */
  getWeatherData: function(latitude, longitude) {
    // 防止重复请求
    if (this.isRequestingWeather) {
      console.log('已有天气请求正在进行，跳过此次请求');
      return;
    }
    this.isRequestingWeather = true;
    
    // 使用代理模式调用天气API
    const weatherProxy = {
      // 真实API请求
      realApiRequest: () => {
        const API_HOST = "mh3p3y76gv.re.qweatherapi.com"; // 您的专属API Host
        
        wx.request({
          url: `https://${API_HOST}/v7/weather/now?key=${this.data.weatherKey}&location=${longitude},${latitude}`,
          success: res => {
            // 重置请求标志
            this.isRequestingWeather = false;
            //天气数据
          //  console.log('天气API返回数据:', res.data);
            if (res.data && res.data.code === '200' && res.data.now) {
              const weatherData = res.data.now;
              
              // 根据icon代码映射到TDesign图标
              const tDesignIcon = this.mapWeatherIconToTDesign(weatherData.icon);
              
              // 构建完整的天气数据对象
              const completeWeatherData = {
                temp: weatherData.temp,
                text: weatherData.text,
                icon: weatherData.icon,
                tDesignIcon: tDesignIcon,
                location: this.data.weatherData.location,
                tips: this.getWeatherTips(weatherData.text)
              };
              
              // 设置天气数据
              this.setData({
                weatherData: completeWeatherData
              });
              
              // 保存到全局缓存
              const app = getApp();
              app.globalData = app.globalData || {};
              app.globalData.weatherData = completeWeatherData;
              app.globalData.weatherLastUpdateTime = new Date().getTime();
            } else {
              console.error('获取天气数据失败:', res.data);
              // 使用备用方法
              weatherProxy.backupRequest();
            }
          },
          fail: (err) => {
            // 重置请求标志
            this.isRequestingWeather = false;
            
            console.error('天气API请求失败:', err);
            // 使用备用方法
            weatherProxy.backupRequest();
          }
        });
      },
      
      // 备用方法（显示获取失败）
      backupRequest: () => {
        console.log('天气数据获取失败');
        // 重置请求标志
        this.isRequestingWeather = false;
        
        // 显示获取失败
        this.setData({
          'weatherData.temp': '--',
          'weatherData.text': '获取失败',
          'weatherData.icon': '',
          'weatherData.tips': '天气数据暂时无法获取，请稍后再试'
        });
      },
      
      // 代理方法，根据条件决定调用哪个实际方法
      request: () => {
        try {
          // 默认使用真实API
          weatherProxy.realApiRequest();
        } catch (error) {
          console.error('天气API代理错误:', error);
          // 出错时使用备用方法
          weatherProxy.backupRequest();
        }
      }
    };
    
    // 执行代理请求
    weatherProxy.request();
  },

  /**
   * 将和风天气icon代码映射为TDesign图标名称
   * @param {string} iconCode - 和风天气icon代码
   * @returns {string} - 对应的TDesign图标名称
   */
  mapWeatherIconToTDesign: function(iconCode) {
    // 晴天
    if (iconCode === '100') {
      return 'sunny';
    }
    
    // 多云或晴间多云
    if (['101', '102', '103', '104'].includes(iconCode)) {
      return 'cloudy-sunny';
    }
    
    // 各种雨
    if (['300', '301', '305', '306', '307', '308', '309', '310', 
         '311', '312', '313', '314', '315', '316', '317', '318', '399'].includes(iconCode)) {
      return 'rain-medium';
    }
    
    // 雷雨天气
    if (['302', '303', '304'].includes(iconCode)) {
      return 'thunderstorm';
    }
    
    // 其他天气状况
    return 'cloudy-day';
  },
  
  /**
   * 设置默认天气数据
   */
  setDefaultWeatherData: function() {
    this.setData({
      'weatherData.temp': '25',
      'weatherData.text': '晴',
      'weatherData.icon': '100',
      'weatherData.tDesignIcon': 'sunny',
      'weatherData.location': '未知位置',
      'weatherData.tips': '今天天气不错，适合发布新的供应信息~'
    });
  },

  /**
   * 获取天气提示语
   */
  getWeatherTips: function(weatherText) {
    const tips = {
      '晴': '阳光明媚，适合拍摄供应照片~',
      '多云': '光线柔和，拍照效果更佳！',
      '阴': '光线均匀，适合拍摄植物细节~',
      '雨': '雨后植物更显生机，记得做好防水措施~',
      '雪': '雪景如画，给您的供应添加独特魅力~',
      '雾': '注意保持植物湿润，雾天拍照需注意曝光~'
    };
    
    // 根据天气文本返回对应提示，如果没有匹配则返回默认提示
    for (const key in tips) {
      if (weatherText.includes(key)) {
        return tips[key];
      }
    }
    
    return '今天天气不错，适合发布新的供应信息~';
  },

  /**
   * 启动天气图标动画
   */
  startWeatherAnimation: function() {
    // 每3秒切换一次动画状态，营造呼吸效果
    setInterval(() => {
      this.setData({
        weatherAnimation: !this.data.weatherAnimation
      });
    }, 3000);
  },

  /**
   * 点击头像处理
   */
  onTapAvatar: function() {
    // 如果未登录，跳转到用户页面进行登录
    if (!this.data.isLogined) {
      wx.switchTab({
        url: '/pages/user/user'
      });
      return;
    }
    
    // 如果已登录，跳转到用户页面
    wx.switchTab({
      url: '/pages/user/user'
    });
  },

  /**
   * 修改供应信息
   */
  updateSupply: function(e) {
    // 使用catchtap代替stopPropagation
    // 标记需要刷新数据
    this.markNeedsRefresh();

    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user/my-supply/edit/edit?id=${id}`
    });
  },

  /**
   * 删除供应信息
   */
  removeSupply: function(e) {
    // 使用catchtap代替stopPropagation
    const id = e.currentTarget.dataset.id;
    
    // 获取当前供应项
    const supplyItem = this.data.supplyList.find(item => item._id === id);
    
    if (!supplyItem) {
      wx.showToast({
        title: '未找到供应信息',
        icon: 'none'
      });
      return;
    }
    
    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${supplyItem.title}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4444',
      success: (res) => {
        if (res.confirm) {
          // 用户确认删除，显示加载提示
          wx.showLoading({
            title: '正在删除...',
            mask: true
          });
          
          // 获取数据库实例
          const db = wx.cloud.database();
          
          // 1. 删除数据库中的记录
          db.collection('supply_content').doc(id).remove().then(res => {
            console.log('删除数据库记录成功:', res);

            // 2. 删除对应的Notice记录
            this.deleteRelatedNotice(id);

            // 3. 删除云存储中的图片
            this.deleteSupplyImages(supplyItem);

            // 4. 更新用户的供应数量统计
            this.updateUserSupplyCount();
            
            // 5. 从列表中移除该项
            const newSupplyList = this.data.supplyList.filter(item => item._id !== id);
            const newTotal = this.data.total - 1;
            
            this.setData({
              supplyList: newSupplyList,
              originalSupplyList: newSupplyList,
              total: newTotal >= 0 ? newTotal : 0
            });
            
            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }).catch(err => {
            console.error('删除数据库记录失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  },



  /**
   * 删除相关的Notice记录
   * @param {String} postId - 供应帖子ID
   */
  deleteRelatedNotice: function(postId) {
    const db = wx.cloud.database();

    // 删除notice集合中对应的记录，使用精确的筛选条件
    db.collection('notice').where({
      postId: postId,
      postType: 'supply'  // 确保只删除supply类型的notice
    }).remove().then(res => {
      console.log('删除Supply Notice记录成功:', res);
      if (res.stats && res.stats.removed > 0) {
        console.log(`成功删除 ${res.stats.removed} 条Supply Notice记录`);
      } else {
        console.log('没有找到对应的Supply Notice记录');
      }
    }).catch(err => {
      console.error('删除Supply Notice记录失败:', err);
      // Notice删除失败不影响主流程，只记录错误
    });
  },

  /**
   * 删除供应项的云存储图片
   * @param {Object} supplyItem - 供应项数据
   */
  deleteSupplyImages: function(supplyItem) {
    // 获取用户ID
    const userId = supplyItem.provider && supplyItem.provider.userId;
    const supplyId = supplyItem._id;

    if (!userId || !supplyId) {
      console.error('缺少用户ID或供应ID，无法删除图片');
      return;
    }

    // 收集所有需要删除的图片文件ID
    const allImageFiles = [];

    // 添加老格式图片
    if (supplyItem.imageList && Array.isArray(supplyItem.imageList)) {
      allImageFiles.push(...supplyItem.imageList);
    }

    // 添加新格式图片
    if (supplyItem.newImageList && Array.isArray(supplyItem.newImageList)) {
      const newImageUrls = supplyItem.newImageList
        .filter(item => item && item.url)
        .map(item => item.url);
      allImageFiles.push(...newImageUrls);
    }

    // 去重处理，避免删除重复的文件
    const uniqueImageFiles = [...new Set(allImageFiles)];

    //console.log('准备删除的图片文件:', uniqueImageFiles);
    //console.log(`共 ${uniqueImageFiles.length} 张图片需要删除`);

    // 如果有图片文件需要删除
    if (uniqueImageFiles.length > 0) {
      // 使用批量删除API，一次性删除所有文件
      wx.cloud.deleteFile({
        fileList: uniqueImageFiles,
        success: res => {
          //console.log('批量删除文件成功:', res);
          if (res.fileList) {
            res.fileList.forEach((file, index) => {
              if (file.status === 0) {
             //   console.log(`文件 ${index + 1} 删除成功:`, file.fileID);
              } else {
                //console.error(`文件 ${index + 1} 删除失败:`, file.fileID, file.errMsg);
              }
            });
          }
        },
        fail: err => {
          console.error('批量删除文件失败:', err);
          // 如果批量删除失败，尝试逐个删除
          this.deleteFilesOneByOne(uniqueImageFiles);
        }
      });
    } else {
      console.log('没有图片文件需要删除');
    }
  },

  /**
   * 逐个删除文件（备用方案）
   * @param {Array} fileList - 文件ID列表
   */
  deleteFilesOneByOne: function(fileList) {
    console.log('使用备用方案：逐个删除文件');
    let successCount = 0;
    let failCount = 0;

    fileList.forEach((fileID, index) => {
      wx.cloud.deleteFile({
        fileList: [fileID],
        success: res => {
          successCount++;
         // console.log(`文件 ${index + 1}/${fileList.length} 删除成功:`, fileID);

          // 如果是最后一个文件，输出总结
          if (successCount + failCount === fileList.length) {
           // console.log(`文件删除完成：成功 ${successCount} 个，失败 ${failCount} 个`);
          }
        },
        fail: err => {
          failCount++;
          console.error(`文件 ${index + 1}/${fileList.length} 删除失败:`, fileID, err);

          // 如果是最后一个文件，输出总结
          if (successCount + failCount === fileList.length) {
            //console.log(`文件删除完成：成功 ${successCount} 个，失败 ${failCount} 个`);
          }
        }
      });
    });
  },

  /**
   * 更新用户的供应数量统计
   */
  updateUserSupplyCount: function() {
    const app = getApp();
    const userId = app.globalData.userId || wx.getStorageSync('userId');
    
    if (!userId) {
      console.error('未找到用户ID，无法更新供应数量');
      return;
    }
    
    const db = wx.cloud.database();
    
    // 查询用户当前的供应数量
    db.collection('users').doc(userId).get().then(res => {
      const userData = res.data;
      
      // 如果有supplyCount字段，则减1，但不小于0
      if (userData.supplyCount !== undefined) {
        const newCount = Math.max(0, userData.supplyCount - 1);
        
        // 更新用户数据
        db.collection('users').doc(userId).update({
          data: {
            supplyCount: newCount
          }
        }).then(() => {
          console.log('用户供应数量更新成功');
          
          // 更新全局数据中的supplyCount
          if (app.globalData.userInfo) {
            app.globalData.userInfo.supplyCount = newCount;
            
            // 更新本地存储中的userInfo
            wx.setStorageSync('userInfo', app.globalData.userInfo);
          }
        }).catch(err => {
          console.error('更新用户供应数量失败:', err);
        });
      }
    }).catch(err => {
      console.error('获取用户数据失败:', err);
    });
  },

  /**
   * 处理报价（管理员功能）
   */
  handleQuote: function(e) {
    // 阻止冒泡，避免触发父元素的点击事件
    e.stopPropagation();
    
    const quoteId = e.currentTarget.dataset.id;
    if (!quoteId) {
      return wx.showToast({
        title: '无效的报价ID',
        icon: 'none'
      });
    }
    
    // 跳转到报价详情页，用于管理员处理
    wx.navigateTo({
      url: `/pages/quote/detail/detail?id=${quoteId}&admin=true`
    });
  },
})