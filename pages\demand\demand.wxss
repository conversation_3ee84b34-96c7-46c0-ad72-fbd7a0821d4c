/* pages/demand/demand.wxss */
.container {
  padding: 0;
  background-color: #f4f9f4; /* 更柔和的浅绿色背景，与home页面一致 */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 0; /* 移除顶部内边距，使用flex布局自动排列 */
}

/* 自定义导航栏样式 */
.custom-nav {
  background: #43a047 !important; /* 纯色背景 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important; /* 确保最高层级 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

.custom-nav::after {
  display: none;
}

.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #ffffff !important; /* 白色文字 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-weight: bold;
}

/* 主容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部容器 */
.top-container {
  flex-shrink: 0; /* 不允许压缩 */
  background: linear-gradient(to bottom, #43a047, #e8f5e9); /* 使用渐变但不透明，与home一致 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #ffffff; /* 改为纯白色背景 */
  position: relative;
  overflow: hidden;
}

.search-box {
  display: flex;
  align-items: center;
  flex: 1;
  height: 72rpx;
  background-color: #e8f5e9; /* 使用纯色背景，移除渐变，与home一致 */
  border-radius: 36rpx;
  padding: 0 20rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.25);
  box-shadow: 0 4rpx 15rpx rgba(56, 142, 60, 0.15);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.search-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.1) 5%, transparent 10%),
    radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0.08) 7%, transparent 14%);
  background-size: 200rpx 200rpx, 250rpx 250rpx;
  opacity: 0.8;
  z-index: -1;
}

.search-box:active {
  background-color: #f2f2f2;
  transform: scale(0.98);
}

.search-input {
  flex: 1;
  height: 72rpx;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

.search-clear {
  padding: 10rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10; /* 确保清除按钮在最上层 */
}

/* 增大清除按钮的点击区域 */
.search-clear icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.search-btn {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #43a047, #2e7d32);
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(46, 125, 50, 0.2);
  transition: all 0.3s ease;
}

.search-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(46, 125, 50, 0.1);
}

/* 标签栏样式 */
.tabs-scroll {
  width: 100%;
  white-space: nowrap;
  background: #ffffff; /* 改为纯白色背景 */
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tabs {
  display: flex;
  padding: 0 12rpx;
  background-color: transparent;
  position: relative;
  overflow: hidden;
}


.tab-item {
  position: relative;
  padding: 24rpx 30rpx 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  display: inline-block;
  transition: all 0.3s;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #07c160;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);
}

/* 筛选按钮 */
.filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 30rpx;
  font-size: 26rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  transition: all 0.3s ease;
  margin-left: auto;
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(76, 175, 80, 0.15);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.filter-btn.active {
  color: #fff;
  background: linear-gradient(135deg, #43a047, #2e7d32);
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(46, 125, 50, 0.2);
  border: none;
}

.filter-btn text {
  margin-right: 8rpx;
}

.filter-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

/* 筛选面板 */
.filter-panel {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 80% !important;
  height: 100vh !important; /* 使用视口高度 */
  background-color: #fff !important;
  z-index: 100000 !important; /* 进一步提高z-index，确保高于所有元素 */
  box-shadow: -4rpx 0 20rpx rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  transform: translateX(100%) !important;
  display: flex !important;
  flex-direction: column !important;
  padding-top: 88px !important; /* 导航栏高度 */
  padding-bottom: 30rpx !important; /* 减少底部padding，因为不再有底部按钮 */
  box-sizing: border-box !important; /* 确保padding不会增加元素总高度 */
}

.filter-panel.show {
  transform: translateX(0) !important;
  right: 0 !important;
}

.filter-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #006400; /* 深绿色 */
  margin-bottom: 30rpx;
  /* 移除padding-top，因为已经在filter-panel中设置 */
}

/* 筛选条件标题样式 */
.filter-section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #006400; /* 深绿色 */
  font-weight: bold;
}

/* 清除筛选按钮 */
.filter-clear-btn {
  font-size: 24rpx;
  color: #e53935; /* 红色，更加醒目 */
  font-weight: normal;
  background-color: #fff0f0; /* 淡红色背景 */
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #ffcdd2; /* 淡红色边框 */
  margin-left: 10rpx;
  display: inline-flex; /* 改为inline-flex以便内部元素对齐 */
  align-items: center; /* 垂直居中对齐 */
  box-shadow: 0 2rpx 6rpx rgba(229, 57, 53, 0.15); /* 添加阴影效果 */
  transition: all 0.2s ease;
}

.filter-clear-btn:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.filter-clear-btn text {
  margin-left: 4rpx; /* 图标和文字之间的间距 */
}

/* 筛选区块样式 */
.filter-section {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-section-title {
  font-size: 28rpx;
  color: #006400; /* 深绿色 */
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 质量选项样式 */
.quality-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.quality-option {
  padding: 10rpx 30rpx;
  background-color: #f8f8f8;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  border: 1rpx solid #eee;
  transition: all 0.3s;
}

.quality-option.active {
  background-color: #006400; /* 深绿色 */
  color: #fff;
  border-color: #006400; /* 深绿色 */
}

.filter-actions {
  display: flex;
  border-top: 1rpx solid #eee;
  background-color: #fff; /* 确保背景色 */
  position: sticky; /* 使用sticky定位 */
  bottom: 0; /* 固定在底部 */
  left: 0;
  right: 0;
  z-index: 2; /* 确保按钮在内容之上 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05); /* 添加顶部阴影，区分内容区域 */
}

.reset-btn, .confirm-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.reset-btn {
  color: #666;
  background-color: #f9f9f9;
}

.confirm-btn {
  color: #fff;
  background-color: #006400; /* 深绿色 */
}

/* 筛选遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998; /* 确保遮罩层在悬浮按钮之上，但在筛选面板之下 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 需求列表样式 */
.demand-list {
  flex: 1; /* 自动填充剩余空间 */
  padding: 20rpx 0; /* 上下内边距 */
  background-color: #ffffff; /* 确保列表背景也是白色 */
  overflow-y: auto; /* 允许滚动 */
}

/* 需求项样式 */
.demand-item {
  margin-bottom: 35rpx; /* 增加列表项之间的间距 */
  background: linear-gradient(145deg, #ffffff, #f8f8f8); /* 添加微妙的渐变背景 */
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 100, 0, 0.12); /* 默认绿色阴影 */
  border: 1rpx solid rgba(76, 175, 80, 0.2); /* 默认绿色边框 */
  transition: all 0.3s ease;
  position: relative;
  border-left: none; /* 移除左侧边框 */
}

/* 彩色边框和阴影样式 - 绿色（默认） */
.demand-item {
  border-left: none; /* 移除左侧边框 */
}

/* 彩色边框和阴影样式 - 蓝色 */
.demand-item.color-blue {
  border-left: none; /* 移除左侧边框 */
  box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.12); /* 保留蓝色阴影 */
}

/* 彩色边框和阴影样式 - 橙色 */
.demand-item.color-orange {
  border-left: none; /* 移除左侧边框 */
  box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.12); /* 保留橙色阴影 */
}

/* 顶部彩色条纹 */
.demand-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, #4CAF50, #8BC34A); /* 默认绿色渐变 */
  z-index: 3;
}

.demand-item.color-blue::before {
  background: linear-gradient(to right, #2196F3, #03A9F4); /* 蓝色渐变 */
}

.demand-item.color-orange::before {
  background: linear-gradient(to right, #FF9800, #FFC107); /* 橙色渐变 */
}

.demand-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 100, 0, 0.08);
}

.demand-item-container {
  padding: 20rpx;
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.1); /* 使用绿色边框 */
  position: relative; /* 确保为绝对定位的子元素提供参考 */
  overflow: visible; /* 修改为visible确保分享按钮不被截断 */
}

/* 内容区域（原左侧内容区域，现在占满宽度） */
.demand-item-left {
  width: 100%;
}

/* 标题和联系按钮的容器 */
.title-contact-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 标题区域调整 */
.title-section {
  flex: 1;
  margin-right: 16rpx;
}

/* 修改底部边框颜色 */
.demand-item.color-blue .demand-item-container {
  border-bottom: 1rpx solid rgba(33, 150, 243, 0.1); /* 蓝色边框 */
}

.demand-item.color-orange .demand-item-container {
  border-bottom: 1rpx solid rgba(255, 152, 0, 0.1); /* 橙色边框 */
}

/* 主要内容区域 - 参考supply页面设计增大容器 */
.demand-item-content {
  padding: 15rpx;
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 底部信息区域 */
.demand-item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  border-top: 1rpx solid rgba(76, 175, 80, 0.1); /* 默认绿色边框 */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(232, 245, 233, 0.4)); /* 添加微妙的渐变 */
  position: relative;
  z-index: 2;
}

/* 底部按钮区域 */
.demand-item-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  border-top: 1rpx solid rgba(76, 175, 80, 0.1); /* 默认绿色边框 */
  background: linear-gradient(to bottom, rgba(232, 245, 233, 0.3), rgba(255, 255, 255, 0.8)); /* 默认绿色渐变 */
  gap: 20rpx; /* 按钮之间的间距 */
}

/* 修改底部信息区域边框颜色 */
.demand-item.color-blue .demand-item-footer {
  border-top: 1rpx solid rgba(33, 150, 243, 0.1); /* 蓝色边框 */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(227, 242, 253, 0.4)); /* 蓝色渐变 */
}

.demand-item.color-orange .demand-item-footer {
  border-top: 1rpx solid rgba(255, 152, 0, 0.1); /* 橙色边框 */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 243, 224, 0.4)); /* 橙色渐变 */
}

/* 修改底部按钮区域边框颜色 */
.demand-item.color-blue .demand-item-buttons {
  border-top: 1rpx solid rgba(33, 150, 243, 0.1); /* 蓝色边框 */
  background: linear-gradient(to bottom, rgba(227, 242, 253, 0.3), rgba(255, 255, 255, 0.8)); /* 蓝色渐变 */
}

.demand-item.color-orange .demand-item-buttons {
  border-top: 1rpx solid rgba(255, 152, 0, 0.1); /* 橙色边框 */
  background: linear-gradient(to bottom, rgba(255, 243, 224, 0.3), rgba(255, 255, 255, 0.8)); /* 橙色渐变 */
}

.publish-time {
  font-size: 24rpx;
  color: #757575; /* 灰色 */
}

.category {
  display: inline-block;
  font-size: 24rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #00897b, #00695c); /* 添加渐变背景 */
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-weight: 600;
  border-left: 4rpx solid #00695c; /* 深青绿色边框 */
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  margin-left: auto; /* 将标签推到右侧 */
  flex-shrink: 0;
}

/* 标题卡片样式 */
.title-card {
  display: flex;
  flex-direction: column;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 2;
}

.title-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex-wrap: wrap;
}

.demand-prefix {
  font-weight: bold;
  color: #333333;
  font-size: 32rpx;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  position: relative;
}

.demand-prefix::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  height: 2rpx;
  background-color: rgba(76, 175, 80, 0.4); /* 添加下划线装饰 */
}

.demand-content {
  color: rgb(31, 133, 18);
  font-size: 34rpx;
  font-weight: bold;
}

/* 内容区域样式 */
.content-container {
  display: flex;
  margin-bottom: 10rpx;
  padding: 8rpx;
  align-items: flex-start;
  background-color: rgba(232, 245, 233, 0.4); /* 默认绿色背景 */
  border-radius: 6rpx;
  border-left: 3rpx solid rgba(76, 175, 80, 0.3); /* 默认绿色左侧边框 */
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
  position: relative;
  z-index: 2;
}

/* 修改内容区域颜色 */
.demand-item.color-blue .content-container {
  background-color: rgba(227, 242, 253, 0.4); /* 蓝色背景 */
  border-left: 3rpx solid rgba(33, 150, 243, 0.3); /* 蓝色左侧边框 */
}

.demand-item.color-orange .content-container {
  background-color: rgba(255, 243, 224, 0.4); /* 橙色背景 */
  border-left: 3rpx solid rgba(255, 152, 0, 0.3); /* 橙色左侧边框 */
}

.content-label {
  font-size: 26rpx;
  color: #666; /* 灰色 */
  font-weight: bold;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.content-wrapper {
  display: flex;
 
}

.content {
  font-size: 25rpx;
  color: rgb(12, 0, 0);
  line-height: 1.4;
  text-indent: 0.5em; /* 首行缩进2个字符 */
}

/* 图片和内容布局容器 */
.image-content-container {
  display: flex;
  flex-direction: row;
  gap: 15rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 5rpx; /* 减少内边距增加内容空间 */
}

/* 内容和规格容器 */
.content-specs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  width: calc(100% - 260rpx); /* 调整宽度，参考supply页面 */
  box-sizing: border-box;
  overflow: hidden;
}

/* 规格信息区域 - 统一使用with-image样式 */
.specs-info-with-image {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 保持两列布局 */
  gap: 12rpx; /* 增加项目之间的间距 */
  margin-top: 8rpx;
  width: 100%;
  position: relative;
  z-index: 2;
  padding: 5rpx; /* 添加内边距 */
}

/* 规格项样式 - 有图片时 */
.specs-info-with-image .spec-item {
  display: flex;
  align-items: center;
  background: linear-gradient(to right, rgba(232, 245, 233, 0.6), rgba(255, 255, 255, 0.8)); /* 默认绿色渐变 */
  padding: 2rpx 1rpx;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  width: 100%; /* 确保宽度填满网格单元 */
  min-width: 0; /* 防止内容溢出 */
  border-left: 2rpx solid rgba(76, 175, 80, 0.3); /* 默认绿色左侧边框 */
}

/* 修改规格项颜色 */
.demand-item.color-blue .specs-info-with-image .spec-item {
  background: linear-gradient(to right, rgba(227, 242, 253, 0.6), rgba(255, 255, 255, 0.8)); /* 蓝色渐变 */
  border-left: 2rpx solid rgba(33, 150, 243, 0.3); /* 蓝色左侧边框 */
}

.demand-item.color-orange .specs-info-with-image .spec-item {
  background: linear-gradient(to right, rgba(255, 243, 224, 0.6), rgba(255, 255, 255, 0.8)); /* 橙色渐变 */
  border-left: 2rpx solid rgba(255, 152, 0, 0.3); /* 橙色左侧边框 */
}

/* 规格标签样式 */
.specs-info-with-image .spec-label {
  color: #666; /* 灰色 */
  font-size: 24rpx;
  margin-right: 8rpx;
  font-weight: 600;
  background-color: rgba(76, 175, 80, 0.1); /* 浅绿色背景 */
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  white-space: nowrap; /* 防止标签换行 */
  flex-shrink: 0; /* 防止标签被压缩 */
  letter-spacing: 1rpx; /* 字间距 */
}

/* 米径、高度、地径数值的特殊样式 - 橙色和加粗 */
.specs-info-with-image .spec-value-highlight {
  color: #ff6b00 !important; /* 橙色 */
  font-weight: bold !important; /* 加粗 */
}

.specs-info-with-image .spec-value {
  color: #1f031d; 
  font-size: 24rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8); /* 添加文字阴影增强可读性 */
}

/* 预览图样式 - 参考supply页面设计 */
.preview-image {
  width: 220rpx; /* 增大尺寸 260*/
  height: 220rpx; /* 增大尺寸 */
  border-radius: 8rpx;
  object-fit: cover;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  border: 1rpx solid rgba(76, 175, 80, 0.15); /* 添加边框 */
  flex-shrink: 0; /* 防止图片被压缩 */
}

/* 默认图片样式 */
.default-image {
  width: 220rpx; /* 增大尺寸260 */
  height: 220rpx; /* 增大尺寸 */
  border-radius: 8rpx;
  object-fit: contain;
  background-color: #f8f8f8;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  border: 1rpx solid rgba(76, 175, 80, 0.15); /* 添加边框 */
  flex-shrink: 0; /* 防止图片被压缩 */
}

/* 底部主要信息行 */
.footer-main-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 20rpx;
}

/* 左侧信息组 */
.footer-info-group {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

/* 左侧求购地组 */
.footer-location-group {
  display: flex;
  align-items: center;
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

.location-label {
  font-size: 22rpx;
  color: #ff9800;
  font-weight: 600;
  background: rgba(255, 152, 0, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
}

.location-text {
  color: #4CAF50;
  font-weight: 500;
  max-width: 250rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 右侧状态组 */
.footer-status-group {
 
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 16rpx;
  border-radius: 10rpx;
  font-size: 22rpx;
  font-weight: 600;
  line-height: 1;
  white-space: nowrap;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}
/*报价数和过期标签的背景颜色*/
/* .reply-badge {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
} */

/* .deadline-badge {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
} */

.badge-label {
  font-size: 20rpx;
  font-weight: 500;
  opacity: 0.9;
}

.badge-text {
  font-size: 22rpx;
  font-weight: 700;
}

/* 联系按钮样式 - 底部布局，恢复原始大小 */
.contact-btn {
  /* background: linear-gradient(135deg, #4CAF50, #45a049); */
  color: #4CAF50;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 30rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(76, 175, 80, 0.2);
  min-width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.contact-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(76, 175, 80, 0.4);
}

/* 回价按钮样式 - 底部布局，恢复原始大小 */
.reply-price-btn {
  /* background: linear-gradient(135deg, #ff9800, #f57c00); */
  color: #ff9800;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 30rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 152, 0, 0.2);
  min-width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.reply-price-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(255, 152, 0, 0.4);
}

/* 价格占位元素 - 当没有理想价格时保持布局平衡 */
.price-placeholder {
  min-width: 80rpx;
}

/* 联系人和位置信息 */
.contact-location-price-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 16rpx;
}

.contact-location {
  display: flex;
  flex-direction: column;
  gap: 8rpx; /* 增加间距 */
}

.location-info {
  font-size: 13px;
  color: #d36600;
  margin-left: 10rpx;
  font-weight: 550;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
}

/* 价格和数量信息 */
.price-quantity-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20rpx;
  margin-top: 6rpx;
  margin-left: auto;
}

/* 价格信息容器 */
.price-info-container {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
}

/* 价格信息行 */
.price-info-row {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
  flex-wrap: nowrap;
}

/* 价格值 */
.price-value {
  color: #e53935; /* 红色系 */
  font-weight: 700;
  font-size: 28rpx;
  height: 36rpx;
  line-height: 36rpx;
  margin-left: 6rpx;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8); /* 添加文字阴影增强可读性 */
}

.quantity-info, .price-info, .outdate-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
}

.info-label {
  color: #666;
  margin-right: 8rpx;
  flex-shrink: 0;
}

/* 突出显示的标签样式 - 数量标签 */
.info-label-highlight {
  color: #ffffff;
  margin-right: 8rpx;
  flex-shrink: 0;
  font-weight: 600;
  font-size: 26rpx;
  background: linear-gradient(135deg, #7b1fa2, #6a1b9a); /* 添加渐变背景 */
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  letter-spacing: 1rpx;
}

.info-value {
  color: #7b1fa2; /* 紫色系 */
  font-weight: 600;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8); /* 添加文字阴影增强可读性 */
}

.actions {
  display: flex;
  align-items: center;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  padding: 6rpx 12rpx;
  margin: 0;
  line-height: normal;
  font-size: 24rpx;
  color: #006400; /* 深绿色 */
  border: none;
  border-radius: 4rpx;
  background-color: rgba(0, 100, 0, 0.05); /* 淡绿色背景 */
}

.share-btn::after {
  display: none;
}

.share-btn text {
  margin-left: 6rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #006400; /* 深绿色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 加载更多和没有更多 */
.load-more, .no-more {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx 0;
}

.load-more {
  background-color: rgba(0, 100, 0, 0.05); /* 淡绿色背景 */
  border-radius: 8rpx;
  margin: 10rpx 0;
  padding: 16rpx 0;
  transition: all 0.3s ease;
}

.load-more:active {
  opacity: 0.8;
  background-color: rgba(0, 100, 0, 0.08); /* 深一点的淡绿色 */
}

/* 发布按钮 */
.publish-btn {
  position: fixed;
  right: 40rpx;
  bottom: 190rpx;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #07c160, #00b050); /* 深绿色渐变 */
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 100, 0, 0.4); /* 绿色阴影 */
  z-index: 99;
  padding: 0 20rpx;
  transition: all 0.3s ease;
  flex-direction: row; /* 确保内容横向排列 */
}

.publish-btn-hover {
  transform: translateY(4rpx) scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 100, 0, 0.2);
}

.publish-btn text {
  font-size: 17rpx;
  writing-mode: horizontal-tb !important; /* 强制横向显示 */
  display: inline-block; /* 确保横向显示 */
  transform: rotate(0deg); /* 确保不旋转 */
  color: #f2ffa9; /* 修改为白色，与图标统一 */
  font-weight: 700;
  letter-spacing: 1rpx;
  margin-left: -8rpx;
  white-space: nowrap; /* 禁止文字换行 */
  vertical-align: middle; /* 垂直居中 */
}

/* 回到顶部 */
.back-top {
  position: fixed;
  right: 40rpx;
  bottom: 270rpx;
  width: 64rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 99;
  transition: all 0.3s ease;
}

.back-top-hover {
  transform: translateY(2rpx) scale(0.95);
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 确保筛选面板显示时，悬浮按钮不可见 */
.showFilter .publish-btn,
.showFilter .back-top {
  display: none !important;
}

/* 规格信息区域 - 无图片时 */
.specs-info-no-image {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列布局 */
  gap: 10rpx; /* 项目之间的间距 */
  margin-top: 8rpx;
  width: 100%;
  position: relative;
  z-index: 2;
}

/* 无图片时的规格项样式 */
.specs-info-no-image .spec-item {
  display: flex;
  align-items: center;
  background: linear-gradient(to right, rgba(232, 245, 233, 0.6), rgba(255, 255, 255, 0.8)); /* 默认绿色渐变 */
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  border-left: 2rpx solid rgba(76, 175, 80, 0.3); /* 默认绿色左侧边框 */
}

/* 修改无图片规格项颜色 */
.demand-item.color-blue .specs-info-no-image .spec-item {
  background: linear-gradient(to right, rgba(227, 242, 253, 0.6), rgba(255, 255, 255, 0.8)); /* 蓝色渐变 */
  border-left: 2rpx solid rgba(33, 150, 243, 0.3); /* 蓝色左侧边框 */
}

.demand-item.color-orange .specs-info-no-image .spec-item {
  background: linear-gradient(to right, rgba(255, 243, 224, 0.6), rgba(255, 255, 255, 0.8)); /* 橙色渐变 */
  border-left: 2rpx solid rgba(255, 152, 0, 0.3); /* 橙色左侧边框 */
}

/* 无图片时的规格标签样式 */
.specs-info-no-image .spec-label {
  color: #666; /* 灰色 */
  font-size: 24rpx;
  margin-right: 8rpx;
  font-weight: 600;
  background-color: rgba(76, 175, 80, 0.1); /* 浅绿色背景 */
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  white-space: nowrap;
  flex-shrink: 0;
  letter-spacing: 1rpx;
}

/* 无图片时米径、高度、地径数值的特殊样式 - 橙色和加粗 */
.specs-info-no-image .spec-value-highlight {
  color: #ff6b00 !important; /* 橙色 */
  font-weight: bold !important; /* 加粗 */
}

.specs-info-no-image .spec-value {
  color: #333; /* 深灰色 */
  font-size: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8); /* 添加文字阴影增强可读性 */
}

/* TDesign风格分割线 */
.t-divider {
  display: flex;
  align-items: center;
  margin: 12rpx 0;
  color: #bdbdbd;
  font-size: 24rpx;
  border-color: #eaeaea;
  border-width: 0;
}

/* 修改伪元素选择器为实际元素 */
.t-divider-before {
  flex: 1;
  height: 1rpx;
  background-color: #eaeaea; /* 默认灰色 */
}

.t-divider-after {
  flex: 1;
  height: 1rpx;
  background-color: #eaeaea; /* 默认灰色 */
}

/* 彩色分割线 */
.demand-item .t-divider-before,
.demand-item .t-divider-after {
  background: linear-gradient(to right, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1)); /* 绿色渐变 */
}

.demand-item.color-blue .t-divider-before,
.demand-item.color-blue .t-divider-after {
  background: linear-gradient(to right, rgba(33, 150, 243, 0.1), rgba(33, 150, 243, 0.3), rgba(33, 150, 243, 0.1)); /* 蓝色渐变 */
}

.demand-item.color-orange .t-divider-before,
.demand-item.color-orange .t-divider-after {
  background: linear-gradient(to right, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.3), rgba(255, 152, 0, 0.1)); /* 橙色渐变 */
}

.t-divider-text {
  padding: 0 24rpx;
  color: #bdbdbd;
}

.item-divider {
  height: 1rpx;
  background-color: #eaeaea;
  margin: 0;
  width: 100%;
}

.demand-item:last-child {
  margin-bottom: 0; /* 最后一个项目不需要底部间距 */
}

/* 区域选择器部分 */
.area-picker-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  padding: 24rpx 32rpx;
  border-radius: 8rpx;
  margin-top: 16rpx;
  border-left: 3rpx solid #006400; /* 添加深绿色左边框 */
}


/* 省份选项样式 */
.province-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 10rpx 0;
}

.province-option {
  padding: 8rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  border: 1rpx solid #eee;
  transition: all 0.3s;
  min-width: 80rpx;
  text-align: center;
}

.province-option.active {
  background-color: #006400; /* 深绿色 */
  color: #fff;
  border-color: #006400; /* 深绿色 */
  box-shadow: 0 2rpx 6rpx rgba(0, 100, 0, 0.2);
}

.toggle-supply-btn {
  position: fixed;
  right: 40rpx;
  bottom: 380rpx;
  width: 200rpx;
}

/* 品质项特殊样式 */
.quality-item {
  border-left: 2rpx solid #ff9800 !important; /* 橙色左侧边框 */
  background: linear-gradient(to right, rgba(255, 243, 224, 0.6), rgba(255, 255, 255, 0.8)) !important; /* 橙色渐变 */
}

.quality-value {
  color: #ff9800 !important; /* 橙色文字 */
  font-size: 24rpx;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8); /* 添加文字阴影增强可读性 */
}

.price-tag {
  color: #ff0000;
  font-size: 27rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
 
}

.price-label {
  font-size: 27rpx;
  color: rgb(0, 0, 0);
  background: none;
}

/* 植物名称推荐列表样式 */
.plant-suggestions-container {
  position: fixed;  /* 固定定位 */
  top: calc(88px + 110rpx);  /* 导航栏高度 + 搜索栏高度 */
  left: 30rpx;
  width: calc(100% - 60rpx);
  max-height: 70vh; /* 使用视口高度的70%作为最大高度 */
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  overflow: visible;
  z-index: 10001; /* 确保推荐列表位于最上层，高于所有其他元素 */
  border: 1rpx solid #d9e6df;
}

.plant-suggestions-container::before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: 60rpx; /* 调整小三角的水平位置 */
  width: 20rpx;
  height: 20rpx;
  background-color: #fff;
  transform: rotate(45deg);
  border-left: 1rpx solid #d9e6df;
  border-top: 1rpx solid #d9e6df;
  z-index: 1;
  box-shadow: -2rpx -2rpx 5rpx rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #e8f5ee;
  background: linear-gradient(to right, #e8f5ee, #f0f9f4);
}

.suggestions-title {
  font-size: 26rpx;
  color: #07c160;
  font-weight: 500;
}

.suggestions-close {
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
  transition: all 0.2s;
}

.suggestions-close:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

.suggestions-scroll {
  max-height: calc(70vh - 80rpx); /* 减去标题的高度 */
  padding: 10rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 三列布局的植物推荐列表 */
.suggestion-item {
  display: inline-block;
  width: calc(33.33% - 10rpx);
  margin: 5rpx;
  padding: 12rpx 5rpx;
  box-sizing: border-box;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  transition: all 0.2s;
  border: 1rpx solid #e8f5ee;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item-hover {
  background-color: #e8f5ee;
  transform: scale(0.98);
}

.suggestion-name {
  font-size: 24rpx;
  color: #333;
  white-space: normal;
  word-break: break-all;
  padding: 0 5rpx;
  line-height: 1.3;
}
/* 导航栏占位元素 */
.nav-placeholder {
  width: 100%;
  background: #43a047; /* 与导航栏背景色一致 */
  position: fixed;
  top: 0;
  left: 0;
  z-index: 998; /* 在导航栏下方，顶部容器上方 */
}

